@charset"utf-8";

/**
 * <AUTHOR>
 * @Date     2018-11-02
 */

/******基本默认样式*******/
body, div, h1, h2, h3, h4, h5, h6, p, dl, dt, dd, ul, li, pre, form, fieldset, legend, button, input, textarea, th, td, ol {
    margin: 0;
    padding: 0;
}
*, ::after, ::before {
    box-sizing: border-box;
}
body {
    font: 12px "roboto-light";
    color: #000;
    line-height: 1.5;
    overflow-x: hidden;
}
ol, ul, li {
    list-style-type: none;
    vertical-align: middle;
}
img {
    vertical-align: top;
    border: 0;
    max-width: 100%;
    height: auto;
}
input, select, textarea, button {
    vertical-align: middle;
}
textarea, input {
    outline: none;
    resize: none;
}
input[type='submit'], input[type='button'], button {
    text-indent: 0;
    text-align: center;
    cursor: pointer;
}
label, button, a {
    cursor: pointer;
}
ins, em, b, i {
    text-decoration: none;
    font-style: normal;
}

/* 去掉浏览器默认样式 */
select:focus, textarea:focus, input:focus, button {
    outline: none;
}
/* placeholder文字的初始样式 */
input::-webkit-input-placeholder, input::-moz-input-placeholder, textarea::-webkit-textarea-placeholder, textarea::-moz-textarea-placeholder {
    color: #999;
    transition: color .5s;
}
/* placeholder文字的focus样式 */
input:focus::-webkit-input-placeholder, input:focus::-moz-input-placeholder, input:hover::-webkit-input-placeholder, input:hover::-moz-input-placeholder, textarea:focus::-webkit-input-placeholder, textarea:focus::-moz-input-placeholder, textarea:hover::-webkit-input-placeholder, textarea:hover::-moz-input-placeholder {
    color: #c2c2c2;
}
/* table */
table {
    border-collapse: collapse;
    border-spacing: 0;
    font: inherit;
}
/* a标签 */
a {
    color: inherit;
    text-decoration: none;
}
a[href] {
    cursor: pointer;
}
a:hover {
    text-decoration: none;
    cursor: pointer;
}
a:focus {
    background-color: transparent;
}
h1, h2, h3, h4, h5, h6, em {
    font-weight: normal;
}
a, span, li, b, i, label, p, strong, div, h1, h2, h3, h4, h5, h6, font, small, em, li, pre, form, fieldset, legend, button, input, textarea, th, td {
    font-size: 14px;
}
audio, canvas, progress, video {
    display: inline-block;
    vertical-align: baseline;
}
/*用来解决在安卓上的点击出现篮框问题*/
body {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
/*解决ios上去除微信点击蓝色边框 */
a:focus, input:focus, p:focus, div:focus {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body, html {
    width: 100%!important;
    padding: 0!important;
    height: 100%;
}
body::-webkit-scrollbar, html::-webkit-scrollbar {
    width: 3px;
    border-radius: 1.5px;
}
body::-webkit-scrollbar-button, html::-webkit-scrollbar-button {
    display: none;
}
body::-webkit-scrollbar-track, html::-webkit-scrollbar-track {
    background-color: #eee;
}
body::-webkit-scrollbar-thumb, html::-webkit-scrollbar-thumb {
    background-color: #105da9;
}
/* 浮动与清浮动 */
.fl {
    float: left;
}
.fr {
    float: right;
}
.cl::after {
    content:'\20';
    display: block;
    height: 0;
    line-height: 0;
    font-size: 0;
    clear: both;
    visibility: hidden;
}
.hide {
    display: none;
}
.show {
    display: block;
}
.up-word {
    text-transform: uppercase;
}
.v-show {
    visibility: visible;
}
.v-hide {
    visibility: hidden;
}
.mt-8 {
    margin-top: 8px;
}
.mt-10 {
    margin-top: 10px;
}
.mt-12 {
    margin-top: 12px;
}
.mt-24 {
    margin-top: 24px;
}
.mt-32 {
    margin-top: 32px;
}
.mt-36 {
    margin-top: 36px;
}
.mt-48 {
    margin-top: 48px;
}
.mt-15 {
    margin-top: 15px;
}
.mt-20 {
    margin-top: 20px;
}
.mt-30 {
    margin-top: 30px;
}
.mt-40 {
    margin-top: 40px;
}
.mt-55 {
    margin-top: 55px;
}
.mt-60 {
    margin-top: 60px;
}
.mb-8 {
    margin-bottom: 8px;
}
.mb-10 {
    margin-bottom: 10px;
}
.mb-12 {
    margin-bottom: 12px;
}
.mb-24 {
    margin-bottom: 24px;
}
.mb-32 {
    margin-bottom: 32px;
}
.mb-36 {
    margin-bottom: 36px;
}
.mb-48 {
    margin-bottom: 48px;
}
.mb-15 {
    margin-bottom: 15px;
}
.mb-20 {
    margin-bottom: 20px;
}
.mb-30 {
    margin-bottom: 30px;
}
.mb-40 {
    margin-bottom: 40px;
}
.mb-55 {
    margin-bottom: 55px;
}
.mb-60 {
    margin-bottom: 60px;
}
.ml-8 {
    margin-left: 8px;
}
.ml-10 {
    margin-left: 10px;
}
.ml-12 {
    margin-left: 12px;
}
.ml-24 {
    margin-left: 24px;
}
.ml-32 {
    margin-left: 32px;
}
.ml-36 {
    margin-left: 36px;
}
.ml-48 {
    margin-left: 48px;
}
.ml-15 {
    margin-left: 15px;
}
.ml-20 {
    margin-left: 20px;
}
.ml-30 {
    margin-left: 30px;
}
.ml-40 {
    margin-left: 40px;
}
.ml-55 {
    margin-left: 55px;
}
.ml-60 {
    margin-left: 60px;
}
.mr-8 {
    margin-right: 8px;
}
.mr-10 {
    margin-right: 10px;
}
.mr-12 {
    margin-right: 12px;
}
.mr-24 {
    margin-right: 24px;
}
.mr-32 {
    margin-right: 32px;
}
.mr-36 {
    margin-right: 36px;
}
.mr-48 {
    margin-right: 48px;
}
.mr-15 {
    margin-right: 15px;
}
.mr-20 {
    margin-right: 20px;
}
.mr-30 {
    margin-right: 30px;
}
.mr-40 {
    margin-right: 40px;
}
.mr-55 {
    margin-right: 55px;
}
.mr-60 {
    margin-right: 60px;
}
.pt-8 {
    padding-top: 8px;
}
.pt-10 {
    padding-top: 10px;
}
.pt-12 {
    padding-top: 12px;
}
.pt-24 {
    padding-top: 24px;
}
.pt-36 {
    padding-top: 36px;
}
.pt-48 {
    padding-top: 48px;
}
.pt-15 {
    padding-top: 15px;
}
.pt-20 {
    padding-top: 20px;
}
.pt-30 {
    padding-top: 30px;
}
.pt-40 {
    padding-top: 40px;
}
.pt-55 {
    padding-top: 55px;
}
.pt-60 {
    padding-top: 60px;
}
.pb-8 {
    padding-bottom: 8px;
}
.pb-10 {
    padding-bottom: 10px;
}
.pb-12 {
    padding-bottom: 12px;
}
.pb-24 {
    padding-bottom: 24px;
}
.pb-36 {
    padding-bottom: 36px;
}
.pb-48 {
    padding-bottom: 48px;
}
.pb-15 {
    padding-bottom: 15px;
}
.pb-20 {
    padding-bottom: 20px;
}
.pb-30 {
    padding-bottom: 30px;
}
.pb-40 {
    padding-bottom: 40px;
}
.pb-55 {
    padding-bottom: 55px;
}
.pb-60 {
    padding-bottom: 60px;
}
.pl-8 {
    padding-left: 8px;
}
.pl-10 {
    padding-left: 10px;
}
.pl-12 {
    padding-left: 12px;
}
.pl-24 {
    padding-left: 24px;
}
.pl-36 {
    padding-left: 36px;
}
.pl-48 {
    padding-left: 48px;
}
.pl-15 {
    padding-left: 15px;
}
.pl-20 {
    padding-left: 20px;
}
.pl-30 {
    padding-left: 30px;
}
.pl-40 {
    padding-left: 40px;
}
.pl-55 {
    padding-left: 55px;
}
.pl-60 {
    padding-left: 60px;
}
.pr-8 {
    padding-right: 8px;
}
.pr-10 {
    padding-right: 10px;
}
.pr-12 {
    padding-right: 12px;
}
.pr-24 {
    padding-right: 24px;
}
.pr-36 {
    padding-right: 36px;
}
.pr-48 {
    padding-right: 48px;
}
.pr-15 {
    padding-right: 15px;
}
.pr-20 {
    padding-right: 20px;
}
.pr-30 {
    padding-right: 30px;
}
.pr-40 {
    padding-right: 40px;
}
.pr-55 {
    padding-right: 55px;
}
.pr-60 {
    padding-right: 60px;
}
.backImg {
    background-size: cover;
}
.text_overflow {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.txt-center {
    text-align: center;
}
.txt-right {
    text-align: right;
}
[data-ahref] {
    cursor: pointer;
}
body {
    background: transparent;
}
.font-40 {
    font-size: 40px;
}
.font-38 {
    font-size: 32px;
}
.font-36 {
    font-size: 32px;
}
.font-34 {
    font-size: 32px;
}
.font-32 {
    font-size: 32px;
}
.font-30 {
    font-size: 30px;
}
.font-28 {
    font-size: 28px;
}
.font-24 {
    font-size: 24px;
}
.font-22 {
    font-size: 22px;
}
.font-20 {
    font-size: 20px;
}
.font-18 {
    font-size: 18px;
}
.font-16 {
    font-size: 16px;
}
.font-14 {
    font-size: 14px;
}
.font-12 {
    font-size: 12px;
}
.op-1 {
    opacity: 0.1;
}
.op-2 {
    opacity: 0.2;
}
.op-3 {
    opacity: 0.3;
}
.op-4 {
    opacity: 0.4;
}
.op-5 {
    opacity: 0.5;
}
.op-6 {
    opacity: 0.6;
}
.op-7 {
    opacity: 0.7;
}
.op-8 {
    opacity: 0.8;
}
.op-9 {
    opacity: 0.9;
}
.op-10 {
    opacity: 1;
}
.font-light {
    font-weight: lighter;
}
.font-bold {
    font-weight: bold;
}
.font-500 {
    font-weight: 500;
}
.pr {
    position: relative;
}
.pa {
    position: absolute;
}
.block {
    display: block;
}
/* 网站基本宽度 */
.container{
    position: relative;
    padding: 0 15px;
    margin: 0 auto;
}
/* 栅格 */
.grid-box{
    display: flex;
    flex-wrap: wrap;
}
.grid-box.two>.column{
    width: 50%;
}
.grid-box.three>.column{
    width: 33.33%;
}
.grid-box.four>.column{
    width: 25%;
}
.grid-box.five>.column{
    width: 20%;
}
.grid-box.six>.column{
    width: 16.6666667%;
}
.grid-box.seven>.column{
    width: 14.285714%;
}
.grid-box.eight>.column{
    width: 12.55%;
}
.grid-box.nine>.column{
    width: 11.11111111%;
}
.grid-box.ten>.column{
    width: 10%;
}
/* 宽度定义 */
.wid-100 {
    width: 100%;
}
.wid-10 {
    width: 10%;
}
.wid-20 {
    width: 20%;
}
.wid-30 {
    width: 30%;
}
.wid-40 {
    width: 40%;
}
.wid-50 {
    width: 50%;
}
.wid-60 {
    width: 60%;
}
.wid-70 {
    width: 70%;
}
.wid-80 {
    width: 80%;
}
.wid-90 {
    width: 90%;
}
.half {
    width: 50%;
}
.layout-middle {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
}
.layout-v-middle {
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
}
.layout-h-middle {
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
}
.layout-tab {
    display: table;
    width: 100%;
}
.layout-tab > .cell {
    display: table-cell;
    vertical-align: middle;
}
.transi {
    transition: all 0.3s ease-out;
    -webkit-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -ms-transition: all 0.3s ease-out;
}

/******响应式手机版头部通用*******/
@media screen and (min-width: 1100px) {
    .header-box {
        display: none;
    }

}
@media screen and (max-width: 1100px) {
    #banner,
    .inner-banner{
        margin-top: 50px;
    }
    .header-box {
        display: block;
    }
    #header {
        display: none;
    }
    .header-box {
        position: fixed;
        width: 100%;
        left: 0;
        top: 0;
        z-index: 99;
        transition: 0.5s;
        background: #fff;
        box-shadow: 0 0 15px 0 rgba(0,0,0,.3);
    }
    .header-box.scoll, .header-box.hnner, .header-box:hover {
        background: #fff;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    }
    .header-box.scoll .nav li a, .header-box.hnner .nav li a, .header-box:hover .nav li a {
        color: #666666;
        opacity: 1;
        filter:alpha(opacity=100);
    }
    .header-box.scoll .nav li a:before, .header-box.hnner .nav li a:before, .header-box:hover .nav li a:before {
        background: #ff2020;
    }
    .header-box.scoll .nav li:hover a, .header-box.scoll .nav li.current a, .header-box.hnner .nav li:hover a, .header-box.hnner .nav li.current a, .header-box:hover .nav li:hover a {
        color: #ff2020;
    }
    .header {
        padding: 0 15px;
        position:relative;
        z-index:111;
        height: 50px;
        background: #fff;
    }
    .logo {
        position: absolute;
        left: 0;
        width: 100%;
        height: 100%;
        top: 0;
        z-index: 56;
        display: flex;
        justify-content:center;
        align-items:center;
    }
    .logo img{
        height: 20px;
        width: auto;
    }
    .header-right {
        float: right;
        margin-top: 24px;
    }
    .nav {
        float: left;
    }
    .nav li {
        float: left;
        padding-right: 115px;
        line-height: 1;
    }
    .nav li:last-child {
        padding-right: 0;
    }
    .nav li a {
        display: block;
        line-height: 1;
        font-size: 16px;
        opacity: 0.6;
        filter:alpha(opacity=60);
        color: #ffffff;
        padding-bottom: 35px;
        position: relative;
    }
    .nav li a:before {
        content:"";
        width: 100%;
        height: 4px;
        background: #fff;
        position: absolute;
        left: 0;
        bottom: -1px;
        transform-origin:100%;
        -webkit-transform-origin:100%;
        -webkit-transition:-webkit-transform .4s cubic-bezier(.4, 0, .2, 1);
        transition:-webkit-transform .4s cubic-bezier(.4, 0, .2, 1);
        transition:transform .4s cubic-bezier(.4, 0, .2, 1);
        transition:transform .4s cubic-bezier(.4, 0, .2, 1), -webkit-transform .4s cubic-bezier(.4, 0, .2, 1);
        transform:scaleX(0);
        -webkit-transform:scaleX(0);
        visibility:hidden\9;
    }
    .nav li:hover a, .nav li.current a {
        opacity: 1;
        filter:alpha(opacity=100);
    }
    .nav li:hover a:before, .nav li.current a:before {
        transform:scaleX(1);
        -webkit-transform:scaleX(1);
        -moz-transform:scaleX(1);
        transform-origin:0;
        -webkit-transform-origin:0;
        visibility:visible\9;
    }
    .menubox {
        width: 100%;
        position: absolute;
        left: 0;
        top: 91px;
        background: #ff2020;
        filter:alpha(opacity=80);
        background: rgba(0, 120, 215, 0.8);
    }
    .menuwr h2 {
        float: left;
        color: #ffffff;
        font-size: 50px;
        font-weight: normal;
        text-transform: uppercase;
        margin-right: 54px;
        line-height: 1;
        margin-top: 12px;
    }
    .menulist {
        float: left;
        padding-left: 45px;
        border-left: 1px solid #fff;
        border-left:1px solid rgba(255, 255, 255, 0.3);
        min-height: 110px;
        margin-top: 12px;
    }
    .meun-down {
        display: none;
    }
    .menuwr {
        padding: 28px 0 32px;
    }
    .menulist li {
        margin-bottom: 16px;
    }
    .menulist li a {
        display: block;
        line-height: 1;
        color: #ffffff;
        font-size: 14px;
    }
    .menulist li a:hover, .menulist li a.active {
        color: #ff9e00;
    }
    .menuimg {
        float: right;
        width: 250px;
        line-height: 1;
        overflow: hidden;
    }
    .menuimg img {
        display: block;
        width: 100%;
        height: auto;
        line-height: 1;
    }
    .sideLayer{
        position: fixed;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 110;
        background: rgba(0,0,0,.3);
        display: none;
    }
    .menubtn {
        width:25px;
        position:absolute;
        left:15px;
        top:0;
        height: 100%;
        z-index: 80;
        display: flex;
        justify-content:center;
        align-items:center;
    }
    .menubtn span {
        display:block;
        width:18px;
        height:3px;
        background:#000;
        position:relative;
        vertical-align: middle;
        -webkit-transition-duration: .3s, .3s;
        -moz-transition-duration: .3s, .3s;
        -ms-transition-duration: .3s, .3s;
        -o-transition-duration: .3s, .3s;
        transition-duration: .3s, .3s;
        -webkit-transition-delay: .3s, 0s;
        -moz-transition-delay: .3s, 0s;
        -ms-transition-delay: .3s, 0s;
        -o-transition-delay: .3s, 0s;
        transition-delay: .3s, 0s;
    }
    .menubtn span:after, .menubtn span:before {
        content:"";
        position: absolute;
        display: inline-block;
        width: 100%;
        height: 3px;
        left: 0;
        background-color: #000;
        -webkit-transition-duration: .3s, .3s;
        -moz-transition-duration: .3s, .3s;
        -ms-transition-duration: .3s, .3s;
        -o-transition-duration: .3s, .3s;
        transition-duration: .3s, .3s;
        -webkit-transition-delay: .3s, 0s;
        -moz-transition-delay: .3s, 0s;
        -ms-transition-delay: .3s, 0s;
        -o-transition-delay: .3s, 0s;
        transition-delay: .3s, 0s
    }
    .menubtn span:before {
        top: -7px;
        -webkit-transition-property: top, transform;
        -moz-transition-property: top, transform;
        -ms-transition-property: top, transform;
        -o-transition-property: top, transform;
        transition-property: top, transform;
        width: 120%;
    }
    .menubtn span:after {
        bottom: -7px;
        -webkit-transition-property: bottom, transform;
        -moz-transition-property: bottom, transform;
        -ms-transition-property: bottom, transform;
        -o-transition-property: bottom, transform;
        transition-property: bottom, transform;
        width: 80%;
    }
    .navigate {
        position:fixed;
        width:80%;
        height:100%;
        background:#fff;
        box-sizing:border-box;
        left:-100%;
        top:50px;
        overflow-y:auto;
        z-index: 120;
        padding:0 2%;
    }
    
    .header-box .navigate .navigateli{
        overflow-y: auto;
        padding-right: 4px;
        max-height: calc(100% - 200px);
    }
    .navigateli {
        width:100%;
    }
    .navigate li {
        border-bottom:1px solid #dfdfdf;
        position: relative;
    }
    .navigate li ul{
        padding-left: 10px;
    }
    .navigate li i.fa{
        position: absolute;
        top:15px;
        right: 10px;
        display: none;
    }
    .navigate li.on >a {
        background:#ff2020;
        color:#fff;
    }
    .navigate li >a {
        display:block;
        text-align:left;
        padding-left: 15px;
        color:#222;
        font-size:16px;
        height:50px;
        line-height:50px;
    }
    .menudowns {
        padding:10px 0 14px;
        border-top:1px solid #dfdfdf;
        display:none;
    }
    .menudowns li {
        margin-bottom:10px;
        text-align:left;
        padding-left: 15px;
    }
    .menudowns li:last-child {
        margin-bottom:0;
    }
    .menudowns li a {
        display:inline-block;
        color:#999999;
        font-size:16px;
        padding-left: 15px;
    }
    .mb-lan{
        position: absolute;
        cursor: pointer;
        right: 15px;
        z-index: 60;
        top: 0;
        height: 100%;
        width: 24px;
        background: url("../images/mobile/mb-globe.png") no-repeat center;
    }
    .mb-lanList{
        position: fixed;
        z-index: 99;
        width: 30%;
        right: 0;
        top: 50px;
        background: #fff;
        padding: 10px 15px;
        box-shadow: 0 0 10px 0 rgba(0,0,0,.3);
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px;
        display: none;
    }
    .mb-lanList li{
        margin-bottom: 10px;
    }
    .mb-lanList li:last-child{
        margin-bottom: 0;
    }
    .mb-lanList li a{
        color: #000;
    }
    .newsear {
        width:100%;
        margin-top: 30px;
    }
    .newsear form {
        border: 1px solid #eee;
        overflow: hidden;
    }
    .newsear .sub {
        width:40px;
        height:40px;
        background:url(../images/mobile/q-11.png) no-repeat center;
        border:0px none;
        float:left;
        background-size:20px;
    }
    .newsear .pt {
        float:left;
        height:40px;
        width:70%;
        border:0px none;
        padding-left:5%;
        font-size:16px;
        border-left: 1px solid #eee;
        outline: none;
    }
    .mb-side-social{
        position: absolute;
        left: 0;
        bottom: 80px;
        width: 100%;
        text-align: center;
    }
    .mb-side-social a{
        width: 25%;
        float: left;
        text-align: center;
    }
    .mb-side-social a .icon{
        width: 34px;
        height: 34px;
        border-radius: 50%;
        background: #ECECEC;
        line-height: 34px;
        color: #888;
    }
}

/****头部语言选择效果*****/
#lanSelBox{
    font-size:16px;
    background:#fff;
    border-radius:4px;
    position: relative;
}
.current_lang{
    cursor:pointer;
    text-transform:uppercase;
    overflow:hidden;
}
.lang.selected{
    display:none;
}
.lang img,
.lang span.lang-txt{
    display:inline-block;
    margin-left:5px;
    vertical-align:middle;
    color: #444444;
}
.lang span.lang-txt{
    position:relative;
    top:-1px;
    font-weight:700;
}
.lang img{
    width:20px;
    margin-left:0;
}
.lang span span{
    color:#999;
    font-weight:400;
}
.lang i.chevron.down{
    font-size:12px;
    position:relative;
    top:-1px;
    margin-left:3px;
}
.more_lang{
    transform:translateY(-20px);
    opacity:0;
    cursor:pointer;
    display:none;
    position: absolute;
    right: 0;
    top: 100%;
    width: 180px;
    background: #fff;
    -webkit-transition: all .3s cubic-bezier(.25, 1.15, .35, 1.15);
    -moz-transition:    all .3s cubic-bezier(.25, 1.15, .35, 1.15);
    -o-transition:      all .3s cubic-bezier(.25, 1.15, .35, 1.15);
    -ms-transition:     all .3s cubic-bezier(.25, 1.15, .35, 1.15);
    transition:         all .3s cubic-bezier(.25, 1.15, .35, 1.15);
}
.more_lang .lang{
    padding: 10px 15px;
}
#lanSelBox.active .more_lang{
    display:block;
}
.more_lang.active{
    opacity:1;
    transform:translateY(-0px);
}
.more_lang .lang:hover{
    background:#5766b2;
    color:#fff;
}
.more_lang .lang:hover span{
    color:#fff;
}
#lanSelBox:hover,
#lanSelBox.active{
    box-shadow:rgba(0,0,0,0.2) 0 5px 15px;
    -webkit-transition: all 0.3s cubic-bezier(0,.99,.44,.99);
    -moz-transition:    all 0.3s cubic-bezier(0,.99,.44,.99);
    -o-transition:      all 0.3s cubic-bezier(0,.99,.44,.99);
    -ms-transition:     all 0.3s cubic-bezier(0,.99,.44,.99);
    transition:         all 0.3s cubic-bezier(0,.99,.44,.99);

}
#lanSelBox.active .more_lang .lang{
    border-top:1px solid #eaeaea;
}


/*******遮罩弹出按钮（包含链接和放大）*******/
 {
    width: 100%;
    overflow: hidden;
    position: relative;
}
 img {
    margin: 0;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    left: 0;
}
 .ovrly {
    position: absolute;
    background: rgba(0, 0, 0, 0.5);
    height: 100%;
    left: 0;
    top: 0;
    width: 100%;
    opacity: 0;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
 .buttons {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
 .buttons>a {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    opacity: 0;
    background-color: rgb(256, 256, 256);
    -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    display: inline-block;
    line-height: 40px;
    font-size: 16px;
    text-align: center;
    text-decoration: none;
    width: 40px;
    height: 40px;
    margin: 0 1px;
    color: rgba(50, 50, 50, 0.9);
    -webkit-transition: all 0.3s cubic-bezier(0, 1.135, 0.73, 1.405);
    -moz-transition: all 0.3s cubic-bezier(0, 1.135, 0.73, 1.405);
    -o-transition: all 0.3s cubic-bezier(0, 1.135, 0.73, 1.405);
    transition: all 0.3s cubic-bezier(0, 1.135, 0.73, 1.405);
}
 .buttons .the_search {
    -webkit-transition-delay: 0.1s;
    -moz-transition-delay: 0.1s;
    -ms-transition-delay: 0.1s;
    transition-delay: 0.1s;
}
:hover .buttons>a {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    opacity: 1;
}
:hover .ovrly {
    opacity: 1;
}
.hwaq_img_btn_02 {
    position: relative;
    overflow: hidden;
    text-align: center;
    cursor: pointer;
}
.hwaq_img_btn_02 .layer, .hwaq_img_btn_02 .layer > a {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.hwaq_img_btn_02 .layer {
    padding: 2em;
    color: #fff;
    text-transform: uppercase;
    font-size: 1.25em;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}
.hwaq_img_btn_02 img {
    opacity: 0.9;
}
.hwaq_img_btn_02 .layer::after, .hwaq_img_btn_02 img, .hwaq_img_btn_02 p {
    -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
    transition: opacity 0.35s, transform 0.35s;
}
.hwaq_img_btn_02 .layer::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
    content:'';
    opacity: 0;
    -webkit-transform: rotate3d(0, 0, 1, 45deg) scale3d(1, 0, 1);
    transform: rotate3d(0, 0, 1, 45deg) scale3d(1, 0, 1);
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
}
.hwaq_img_btn_02 h2, .hwaq_img_btn_02 p {
    opacity: 1;
    -webkit-transform: scale3d(0.8, 0.8, 1);
    transform: scale3d(0.8, 0.8, 1);
}
.hwaq_img_btn_02 h2 {
    padding-top: 26%;
    -webkit-transition: -webkit-transform 0.35s;
    transition: transform 0.35s;
}
.hwaq_img_btn_02 p {
    padding: 0.5em 2em;
    text-transform: none;
    font-size: 0.85em;
    opacity: 0;
}
.hwaq_img_btn_02:hover{
    background: -webkit-linear-gradient(-45deg, #053565 0%, #0066cc 100%);
    background: linear-gradient(-45deg, #053565 0%, #0066cc 100%);
}
.hwaq_img_btn_02:hover img {
    opacity: 0.7;
    -webkit-transform: scale3d(1.05, 1.05, 1);
    transform: scale3d(1.05, 1.05, 1);
}
.hwaq_img_btn_02:hover .layer::after {
    opacity: 1;
    -webkit-transform: rotate3d(0, 0, 1, 45deg) scale3d(1, 1, 1);
    transform: rotate3d(0, 0, 1, 45deg) scale3d(1, 1, 1);
}
.hwaq_img_btn_02:hover h2, .hwaq_img_btn_02:hover p {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
}
.icon-eye::before {
    content: '\e000';
}
.icon-paper-clip::before {
    content: '\e001';
}
.icon-heart::before {
    content: '\e024';
}
.hwaq_img_btn_02 h2 {
    display: inline-block;
}
.hwaq_img_btn_02:hover p.description {
    opacity: 1;
}
.hwaq_img_btn_02:hover figcaption,
.hwaq_img_btn_02:hover h2,
.hwaq_img_btn_02:hover p.icon-links a {
    -webkit-transform: translate3d(0,0,0);
    transform: translate3d(0,0,0);
}
.hwaq_img_btn_02:hover h2 {
    -webkit-transition-delay: 0.05s;
    transition-delay: 0.05s;
}
.hwaq_img_btn_02:hover p.icon-links a:nth-child(3) {
    -webkit-transition-delay: 0.1s;
    transition-delay: 0.1s;
}
.hwaq_img_btn_02:hover p.icon-links a:nth-child(2) {
    -webkit-transition-delay: 0.15s;
    transition-delay: 0.15s;
}
.hwaq_img_btn_02:hover p.icon-links a:first-child {
    -webkit-transition-delay: 0.2s;
    transition-delay: 0.2s;
}


/*******按钮效果*******/
.hwaq_just_btn_01 {
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
    position: relative;
}
.hwaq_just_btn_01::before {
    content:'';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: rgba(255, 255, 255, 0.25);
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.hwaq_just_btn_01>span{
    position: relative;
    z-index:20;
}
.hwaq_just_btn_01:hover::before {
    opacity: 0;
    -webkit-transform: scale(0.5, 0.5);
    transform: scale(0.5, 0.5);
}
.hwaq_just_btn_01::after {
    content:'';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 1px solid rgba(255, 255, 255, 0.5);
    -webkit-transform: scale(1.2, 1.2);
    transform: scale(1.2, 1.2);
}
.hwaq_just_btn_01:hover::after {
    opacity: 1;
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
}


/**************图片点击放大效果****************/
.mfp-zoom-in .mfp-with-anim {
    opacity: 0;
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
}
.mfp-zoom-in.mfp-bg {
    opacity: 0;
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
}
.mfp-zoom-in.mfp-ready .mfp-with-anim {
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}
.mfp-zoom-in.mfp-ready.mfp-bg {
    opacity: 0.8;
}
.mfp-zoom-in.mfp-removing .mfp-with-anim {
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
    opacity: 0;
}
.mfp-zoom-in.mfp-removing.mfp-bg {
    opacity: 0;
}


/**************页面右下角悬浮表单****************/
.mlztx {
    opacity: .6;
    width: 60px;
    height: 60px;
    border-radius: 60px;
    background-color: #2b93e6;
    position: fixed;
    bottom: 50px;
    right: 20px;
    font-size: 30px;
    text-align: center;
    line-height: 55px;
    color: #fff;
    transition: all 1s ease 0s;
    z-index: 99;
    -webkit-box-shadow: 0px 0px 2px 3px hsla(0,0%,0%,0.1);
    box-shadow: 0px 0px 2px 3px hsla(0,0%,0%,0.1);
    cursor: pointer;
}
.mlztx:hover{
    opacity:1;
    background-color:#00b1ff;
    width:65px;
    height:65px;
}
.foot_gotop{
    cursor: pointer;
    opacity: .6;
    width: 60px;
    height: 60px;
    border-radius: 60px;
    background-color: #2b93e6;
    position: fixed;
    bottom: 120px;
    right: 20px;
    font-size: 30px;
    text-align: center;
    line-height: 55px;
    color: #fff;
    transition: all 1s ease 0s;
    z-index: 99;
    -webkit-box-shadow: 0px 0px 2px 3px hsla(0,0%,0%,0.1);
    box-shadow: 0px 0px 2px 3px hsla(0,0%,0%,0.1);
}
.foot_gotop:hover{
    opacity:1;
    background-color:#00b1ff;
    width:65px;
    height:65px;
}
.layer-form{
    display: none;
}
.container-contact100 {
    width: 100%;
    min-height: 100vh;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 15px;
    background: rgba(0,0,0,0.8);
    position: fixed;
    z-index: 1000001;
    left: 0;
    top: 0;
}
.wrap-contact100 {
    width: 560px;
    background: #fff;
    border-radius: 3px;
    position: relative;
    margin-top: 40px;
}
.contact100-form-title {
    font-size: 30px;
    color: #fff;
    line-height: 1.2;
    text-transform: uppercase;
    background-image: url("../images/basic/layer-form-bg.jpg");
    width: 100%;
    min-height: 128px;
    position: relative;
    z-index: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    align-items: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    overflow: hidden;
    padding: 30px 15px 30px 15px;
}
.contact100-form-title::before {
    content: "";
    display: block;
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(67,67,67,0.6);
}
.contact100-form {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 40px 55px 48px 55px;
}
.wrap-input100 {
    width: 100%;
    position: relative;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    margin-bottom: 15px
}
.label-input100 {
    font-size: 18px;
    color: #999999;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 60px;
    height: 100%;
    top: 0;
    left: 2px;
}
.label-input100.rs1 {
    height: 60px;
}
.input100 {
    display: block;
    width: 100%;
    background: transparent;
    font-size: 15px;
    color: #333333;
    line-height: 1.2;
    padding: 0 25px 0 60px;
    border: none;
}
input.input100 {
    height: 55px;
}
textarea.input100 {
    min-height: 162px;
    padding-top: 22px;
    padding-bottom: 15px;
    font-family: 'microsoft yahei';
}
.focus-input100 {
    position: absolute;
    display: block;
    width: calc(100% + 2px);
    height: calc(100% + 2px);
    top: -1px;
    left: -1px;
    pointer-events: none;
    border: 1px solid;
    border-radius: 2px;
    border-color: #0066cc;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
    -webkit-transform: scaleX(1.1) scaleY(1.3);
    -moz-transform: scaleX(1.1) scaleY(1.3);
    -ms-transform: scaleX(1.1) scaleY(1.3);
    -o-transform: scaleX(1.1) scaleY(1.3);
    transform: scaleX(1.1) scaleY(1.3);
}
.input100:focus + .focus-input100 {
    visibility: visible;
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}
.input100:focus + .focus-input100 + .label-input100 {
    color: #0066cc;
}
.container-contact100-form-btn {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding-top: 17px;
}
.contact100-form-btn {
    font-size: 12px;
    color: #fff;
    line-height: 1.2;
    text-transform: uppercase;
    border: none;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 20px;
    min-width: 160px;
    height: 42px;
    border-radius: 21px;
    background: #0066cc;
    cursor: pointer;
    box-shadow: 0 10px 30px 0px rgba(132, 106, 221, 0.5);
    -moz-box-shadow: 0 10px 30px 0px rgba(132, 106, 221, 0.5);
    -webkit-box-shadow: 0 10px 30px 0px rgba(132, 106, 221, 0.5);
    -o-box-shadow: 0 10px 30px 0px rgba(132, 106, 221, 0.5);
    -ms-box-shadow: 0 10px 30px 0px rgba(132, 106, 221, 0.5);
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
}
.contact100-form-btn:hover {
    background: #333333;
    box-shadow: 0 10px 30px 0px rgba(51, 51, 51, 0.5);
    -moz-box-shadow: 0 10px 30px 0px rgba(51, 51, 51, 0.5);
    -webkit-box-shadow: 0 10px 30px 0px rgba(51, 51, 51, 0.5);
    -o-box-shadow: 0 10px 30px 0px rgba(51, 51, 51, 0.5);
    -ms-box-shadow: 0 10px 30px 0px rgba(51, 51, 51, 0.5);
}
@media (max-width: 576px) {
    .contact100-form {
        padding: 40px 15px 48px 15px;
    }

    .input100 {
        padding: 0 25px 0 40px;
    }

    .label-input100 {
        width: 40px;
    }
}
.validate-input {
    position: relative;
}
.alert-validate .focus-input100 {
    box-shadow: 0 5px 20px 0px rgba(250, 66, 81, 0.1);
    -moz-box-shadow: 0 5px 20px 0px rgba(250, 66, 81, 0.1);
    -webkit-box-shadow: 0 5px 20px 0px rgba(250, 66, 81, 0.1);
    -o-box-shadow: 0 5px 20px 0px rgba(250, 66, 81, 0.1);
    -ms-box-shadow: 0 5px 20px 0px rgba(250, 66, 81, 0.1);
}
.alert-validate::before {
    content: "";
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    position: absolute;
    width: calc(100% + 2px);
    height: calc(100% + 2px);
    background-color: transparent;
    border: 1px solid #fa4251;
    border-radius: 2px;
    top: -1px;
    left: -1px;
    pointer-events: none;
}
.btn-hide-validate {
    font-size: 18px;
    color: #fa4251;
    cursor: pointer;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    height: 100%;
    top: 0px;
    right: -25px;
}
.rs1-alert-validate.alert-validate::before {
    background-color: #fff;
}
.true-validate::after {
    content: "\f26b";
    font-size: 18px;
    color: #00ad5f;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    height: 100%;
    top: 0px;
    right: -25px;
}
@media (max-width: 576px) {
    .btn-hide-validate {
        right: 10px;
    }
    .true-validate::after {
        right: 10px;
    }
}
.btn-hide-contact100 {
    font-size: 20px;
    color: #fff;
    opacity: 0.6;
    position: absolute;
    right: 0;
    top: -34px;
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
    cursor: pointer;
}
.btn-hide-contact100 .icon{
    margin: 0;
    padding: 0;
    background: none;
}
.btn-hide-contact100:hover {
    opacity: 1;
}


/*******图片移上放大效果*******/
.img-box{
    overflow: hidden;
    position: relative;
}
.img-box img{
    transition: 1s ease;
}
.img-box:hover img{
    transform: scale(1.1,1.1);
}

/*******Swiper外部容器超出隐藏*******/
.slideBox{
    overflow: hidden;
}

/*******button-hover（边框线切换）*******/
.hwaq-hover-btn{
    display: inline-block;
    position: relative;
}
.hwaq-hover-btn span{
    display: block;
    transition: all 0.5s ease 0s;
}
.hwaq-hover-btn:hover span{ color: #ff5c19; }
.hwaq-hover-btn:before,
.hwaq-hover-btn:after,
.hwaq-hover-btn span:before,
.hwaq-hover-btn span:after{
    content: "";
    width: 25%;
    height: 100%;
    border-radius: 30px;
    border: 2px solid #ff5c19;
    border-left: none;
    border-right: none;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease 0s;
}
.hwaq-hover-btn:after{ left: 25%; }
.hwaq-hover-btn span:before{ left: 50%; }
.hwaq-hover-btn span:after{ left: 75%; }
.hwaq-hover-btn:hover:before,
.hwaq-hover-btn:hover:after,
.hwaq-hover-btn:hover span:before,
.hwaq-hover-btn:hover span:after{
    border-radius: 0;
    opacity: 1;
    transform: scale(1);
}
.hwaq-hover-btn:hover:before{ border-left: 2px solid #ff5c19; }
.hwaq-hover-btn:hover span:after{ border-right: 2px solid #ff5c19; }

/*******button-hover（横向闪过）*******/
.set_1_btn {
    color: #333;
    cursor: pointer;
    display: block;
    position: relative;
    vertical-align: middle;
    background:transparent;
}
.set_1_btn:hover {
    text-decoration: none;
    transition: all 150ms linear 0s;
}
.set_1_btn svg {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}
.set_1_btn rect {
    fill: none;
    stroke: #e1b700;
    stroke-width: 2;
    stroke-dasharray: 422, 0;
    transition: all 450ms linear 0s;
}
.set_1_btn:hover rect {
    stroke-width: 5;
    stroke-dasharray: 15, 310;
    stroke-dashoffset: 114;
    -webkit-transition: all 1.35s cubic-bezier(0.19, 1, 0.22, 1);
    transition: all 1.35s cubic-bezier(0.19, 1, 0.22, 1);
}

/*******img-hover（遮罩+链接、放大）*******/
.portfolio-item {
    position: relative;
    overflow: hidden;
    width: 300px;
    height: 300px;
}
.portfolio-item img {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: auto;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.portfolio-item__info {
    position: absolute;
    top: 7px;
    left: 7px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: calc(100% - 2 * 7px);
    height: calc(100% - 2 * 7px);
    background-color: rgba(38, 37, 37, 0.5);
}
.portfolio-item__header {
    position: relative;
    margin: 0 0 20px 0;
    padding: 15px 0;
    font-size: 22px;
}
.portfolio-item__header:after {
    position: absolute;
    left: 0;
    bottom: 0;
    display: block;
    height: 2px;
    width: 100%;
    content:'';
    background-color: #8D909B;
}
.portfolio-item__links {
    display: flex;
}
.portfolio-item__link-block {
    position: relative;
    width: 35px;
    height: 35px;
    margin-right: 10px;
}
.portfolio-item__link-block:last-child {
    margin-right: 0;
}
.portfolio-item__link {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #101010;
    text-decoration: none;
    border: 1px solid #101010;
    border-radius: 50%;
}
.portfolio-item__link:hover {
    color: #fff;
    background-color: #101010;
}
.portfolio-item--eff3 .portfolio-item__info {
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
    transition-duration: 0.3s;
    transition-timing-function: linear;
    transition-delay: 0s;
    -webkit-transform: scale(0, 1);
    transform: scale(0, 1);
    -webkit-transform-origin: right top;
    transform-origin: right top;
}
.portfolio-item--eff3 .portfolio-item__header {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    opacity: 0;
}
.portfolio-item--eff3 .portfolio-item__header:after {
    bottom: -20px;
    opacity: 0;
}
.portfolio-item--eff3 .portfolio-item__link-block {
    opacity: 0;
}
.portfolio-item--eff3 .portfolio-item__link-block:first-child {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}
.portfolio-item--eff3 .portfolio-item__link-block:nth-child(2) {
    -webkit-transform: translateX(50%);
    transform: translateX(50%);
}
.portfolio-item--eff3:hover .portfolio-item__info {
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
    transition-duration: 0.3s;
    transition-timing-function: linear;
    transition-delay: 0s;
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    -webkit-transform-origin: left top;
    transform-origin: left top;
}
.portfolio-item--eff3:hover .portfolio-item__header {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0.45s;
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
}
.portfolio-item--eff3:hover .portfolio-item__header:after {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0.6s;
    bottom: 0;
    opacity: 1;
}
.portfolio-item--eff3:hover .portfolio-item__link-block {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0.85s;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
}

/*******img-hover（图片翻盖效果）*******/
.hwaq-img-fg-box{
    position:relative;
}
.hwaq-img-fg-box .pic{
    position: relative;
    z-index: 1;
    transform-origin: 95% 50% 0;
    transition: all 0.5s ease 0s;
}
.hwaq-img-fg-box:hover .pic{
    transform:rotate(-160deg);
    z-index: 10;
}
.hwaq-img-fg-box .pic img{
    width:100%;
    height:auto;
    border-radius:50%;
}
.hwaq-img-fg-box .content{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #b9607e;
}

/*animate补充动画*/
@keyframes fadeInLeftSmall {
    from {
        opacity: 0;
        transform: translate3d(-10%, 0, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}
.fadeInLeftSmall {
    animation-name: fadeInLeftSmall;
}
@keyframes fadeInRightSmall {
    from {
        opacity: 0;
        transform: translate3d(10%, 0, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}
.fadeInRightSmall {
    animation-name: fadeInRightSmall;
}
@keyframes fadeInDownSmall {
    from {
        opacity: 0;
        transform: translate3d(0%, -10%, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}
.fadeInDownSmall {
    animation-name: fadeInDownSmall;
}
@keyframes fadeOutDownSmall {
    from {
        opacity: 1;
        transform: none;
    }
    to {
        opacity: 0;
        transform: translate3d(0%, 10%, 0);
    }
}
.fadeOutDownSmall {
    animation-name: fadeOutDownSmall;
}
@keyframes fadeInUpSmall {
    from {
        opacity: 0;
        transform: translate3d(0%, 10%, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}
.fadeInUpSmall {
    animation-name: fadeInUpSmall;
}
.Txtblur1 {
    animation-duration: 1.2s!important;
    animation: Txtblur cubic-bezier(0.82, 0.01, 0.34, 1);
}
.Txtblur2 {
    animation-duration: 1s!important;
    animation: Txtblur cubic-bezier(0.82, 0.01, 0.34, 1);
}
.scaleIn {
    animation-duration: 0.8s!important;
    animation: scaleIn cubic-bezier(0.82, 0.01, 0.34, 1);
}
@keyframes Txtblur {
    0% {
        filter: blur(12px);
        opacity: 0;
        transform: scale(1.2);
        -webkit-transform: scale(1.2);
        -moz-transform: scale(1.2);
        -o-transform: scale(1.2);
        -ms-transform: scale(1.2);
    }
    100% {
        filter: blur(0);
        opacity: 1;
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
    }
}
@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(1.1);
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -o-transform: scale(1.1);
        -ms-transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
    }
}

.scale_go {
    animation: scale_go 3s cubic-bezier(0.34, 0.9, 1, 1);
    animation-duration: 3s!important;
}

@keyframes scale_go {
    from {
        transform: scale(1.3);
        -webkit-transform: scale(1.3);
        -moz-transform: scale(1.3);
        -o-transform: scale(1.3);
        -ms-transform: scale(1.3);
    }
    to {
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
    }
}