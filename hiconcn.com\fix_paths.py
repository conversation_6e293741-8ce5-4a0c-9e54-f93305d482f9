#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def fix_html_paths(file_path, depth=0):
    """修复HTML文件中的路径"""
    
    # 根据文件深度确定相对路径前缀
    prefix = "../" * depth
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复CSS和JS路径
    content = re.sub(r'href="/template/cn/css/', f'href="{prefix}assets/css/', content)
    content = re.sub(r'src="/template/cn/js/', f'src="{prefix}assets/js/', content)
    content = re.sub(r'src="/template/cn/images/', f'src="{prefix}assets/images/', content)
    
    # 修复图片路径
    content = re.sub(r'src="/hiconcn/[^"]*/', f'src="{prefix}assets/images/', content)
    content = re.sub(r'src="/hisurp/[^"]*/', f'src="{prefix}assets/images/', content)
    content = re.sub(r'data-src="/hiconcn/[^"]*/', f'data-src="{prefix}assets/images/', content)
    content = re.sub(r'data-src="/hisurp/[^"]*/', f'data-src="{prefix}assets/images/', content)
    content = re.sub(r'data-src="assets/images/[^"]*/', f'data-src="{prefix}assets/images/', content)

    # 将data-src改为src以修复懒加载问题
    content = re.sub(r'data-src="([^"]*)"', r'src="\1"', content)
    content = re.sub(r'class="swiper-lazy"', 'class=""', content)
    
    # 修复API路径
    content = re.sub(r'href="/api/min/\?f=template/cn/css/[^"]*"', f'href="{prefix}assets/css/basic.css">\n<link rel="stylesheet" href="{prefix}assets/css/main.css">\n<link rel="stylesheet" href="{prefix}assets/css/swiper.min.css">\n<link rel="stylesheet" href="{prefix}assets/css/slick.css"', content)
    content = re.sub(r'src="/api/min/\?f=template/cn/js/[^"]*"', f'src="{prefix}assets/js/jquery.js"></script>\n<script type="text/javascript" src="{prefix}assets/js/swiper.min.js"', content)
    
    # 修复页面链接
    if depth == 0:  # 根目录文件
        content = re.sub(r'href="/"', 'href="index.html"', content)
        content = re.sub(r'href="/product/"', 'href="product/index.html"', content)
        content = re.sub(r'href="/about/"', 'href="about/company-profile.html"', content)
        content = re.sub(r'href="/service/"', 'href="service/index.html"', content)
        content = re.sub(r'href="/news/"', 'href="news/index.html"', content)
        content = re.sub(r'href="/contact/"', 'href="contact/index.html"', content)
        content = re.sub(r'href="/technology/advanced-equipment.html"', 'href="technology/advanced-equipment.html"', content)
    else:  # 子目录文件
        content = re.sub(r'href="/"', 'href="../index.html"', content)
        content = re.sub(r'href="/product/"', 'href="../product/index.html"', content)
        content = re.sub(r'href="/about/"', 'href="../about/company-profile.html"', content)
        content = re.sub(r'href="/service/"', 'href="../service/index.html"', content)
        content = re.sub(r'href="/news/"', 'href="../news/index.html"', content)
        content = re.sub(r'href="/contact/"', 'href="../contact/index.html"', content)
        content = re.sub(r'href="/technology/advanced-equipment.html"', 'href="../technology/advanced-equipment.html"', content)
    
    # 移除图片URL参数
    content = re.sub(r'\?imageView2/2/format/jp2', '', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复: {file_path}")

def main():
    # 修复根目录的index.html (深度0)
    if os.path.exists('index.html'):
        fix_html_paths('index.html', 0)
    
    # 修复子目录的HTML文件 (深度1)
    subdirs = ['product', 'about', 'service', 'news', 'contact', 'technology']
    
    for subdir in subdirs:
        if os.path.exists(subdir):
            for file in os.listdir(subdir):
                if file.endswith('.html'):
                    file_path = os.path.join(subdir, file)
                    fix_html_paths(file_path, 1)

if __name__ == "__main__":
    main()
