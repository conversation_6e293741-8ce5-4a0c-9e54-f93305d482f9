# 宁波惠康实业有限公司网站本地版本

## 简介
这是从 https://www.hiconcn.com/ 下载的网站本地版本，包含了主要页面和资源文件。

## 包含的页面
- **主页**: index.html
- **产品展示**: product/index.html
- **公司简介**: about/company-profile.html
- **服务**: service/index.html
- **生产设备**: technology/advanced-equipment.html
- **新闻中心**: news/index.html
- **联系我们**: contact/index.html

## 文件结构
```
hiconcn.com/
├── index.html                          # 主页
├── assets/                             # 静态资源
│   ├── css/                           # CSS样式文件
│   │   ├── basic.css
│   │   ├── main.css
│   │   ├── swiper.min.css
│   │   ├── slick.css
│   │   └── font-awesome.min.css
│   ├── js/                            # JavaScript文件
│   │   ├── jquery.js
│   │   └── swiper.min.js
│   └── images/                        # 图片文件
│       ├── logo.png
│       ├── logo2.png
│       ├── logo-h.png
│       ├── icon.ico
│       └── ...
├── product/
│   └── index.html                     # 产品展示页面
├── about/
│   └── company-profile.html           # 公司简介页面
├── service/
│   └── index.html                     # 服务页面
├── technology/
│   └── advanced-equipment.html        # 生产设备页面
├── news/
│   └── index.html                     # 新闻中心页面
├── contact/
│   └── index.html                     # 联系我们页面
└── README.md                          # 说明文件
```

## 如何使用

### 方法1: 直接打开文件
直接双击 `index.html` 文件在浏览器中打开。

### 方法2: 使用本地服务器（推荐）
1. 打开命令行/终端
2. 进入网站目录：
   ```bash
   cd "d:\空调网站原型图\123\hiconcn.com"
   ```
3. 启动Python内置服务器：
   ```bash
   python -m http.server 8000
   ```
4. 在浏览器中访问：http://localhost:8000

### 方法3: 使用其他本地服务器
如果安装了Node.js，可以使用：
```bash
npx serve .
```

## 注意事项
1. 所有路径已修改为相对路径，可以在本地正常浏览
2. 部分动态功能（如搜索、表单提交）可能无法正常工作
3. 图片已下载到本地，但可能不包含所有原网站的图片
4. 建议使用本地服务器方式访问以获得最佳体验

## 技术说明
- 使用curl下载了原网站的HTML页面
- 下载了主要的CSS、JS和图片资源
- 使用Python脚本批量修改了所有路径为相对路径
- 保持了原网站的布局和样式

## 联系信息
原网站：https://www.hiconcn.com/
公司：宁波惠康实业有限公司
