/* 修复轮播图样式 */
#banner {
    position: relative;
    overflow: hidden;
    height: 500px;
}

#banner ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

#banner li {
    position: relative;
    width: 100%;
    height: 500px;
}

#banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Slick轮播图样式 */
.slick-dots {
    bottom: 20px;
    text-align: center;
}

.slick-dots li {
    display: inline-block;
    margin: 0 5px;
}

.slick-dots li button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    border: none;
    text-indent: -9999px;
    cursor: pointer;
}

.slick-dots li.slick-active button {
    background: #fff;
}

.slick-prev, .slick-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 50px;
    height: 50px;
    background: rgba(0,0,0,0.5);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 18px;
}

.slick-prev {
    left: 20px;
}

.slick-next {
    right: 20px;
}

.slick-prev:hover, .slick-next:hover {
    background: rgba(0,0,0,0.8);
}

/* 产品轮播样式 */
.product-list .slick-track {
    display: flex;
    align-items: stretch;
}

.product-list .swiper-slide {
    height: auto;
    margin: 0 10px;
}

/* 设备轮播样式 */
.equipment-list .slick-track {
    display: flex;
    align-items: stretch;
}

.equipment-list .swiper-slide {
    height: auto;
    margin: 0 10px;
}

/* 新闻轮播样式 */
.new-list .slick-track {
    display: flex;
    align-items: stretch;
}

.new-list .swiper-slide {
    height: auto;
    margin: 0 10px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    #banner {
        height: 300px;
    }
    
    #banner li {
        height: 300px;
    }
    
    .slick-prev, .slick-next {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
    
    .slick-prev {
        left: 10px;
    }
    
    .slick-next {
        right: 10px;
    }
}
